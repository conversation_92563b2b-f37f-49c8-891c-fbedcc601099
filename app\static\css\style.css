/* ===== MODERN FUTURISTIC POS SYSTEM STYLES ===== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* CSS Variables for Modern Theme */
:root {
    /* Primary Colors - Futuristic Blue/Purple Gradient */
    --primary-50: #f0f4ff;
    --primary-100: #e0edff;
    --primary-200: #c7ddff;
    --primary-300: #a4c7ff;
    --primary-400: #7ca6ff;
    --primary-500: #5b82ff;
    --primary-600: #4c63d2;
    --primary-700: #3d4ba6;
    --primary-800: #2e3a7a;
    --primary-900: #1f2951;

    /* Secondary Colors - Electric Cyan */
    --secondary-50: #f0fdff;
    --secondary-100: #ccfbff;
    --secondary-200: #99f6ff;
    --secondary-300: #66f0ff;
    --secondary-400: #33e9ff;
    --secondary-500: #00e1ff;
    --secondary-600: #00b8d4;
    --secondary-700: #008fa3;
    --secondary-800: #006672;
    --secondary-900: #003d41;

    /* Accent Colors - Neon Green */
    --accent-50: #f0fff4;
    --accent-100: #dcfce7;
    --accent-200: #bbf7d0;
    --accent-300: #86efac;
    --accent-400: #4ade80;
    --accent-500: #22c55e;
    --accent-600: #16a34a;
    --accent-700: #15803d;
    --accent-800: #166534;
    --accent-900: #14532d;

    /* Neutral Colors - Dark Theme */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;

    /* Glass Effect */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-500) 0%, var(--accent-500) 100%);
    --gradient-dark: linear-gradient(135deg, var(--neutral-900) 0%, var(--neutral-800) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-neon: 0 0 20px rgba(91, 130, 255, 0.3);
    --shadow-glow: 0 0 30px rgba(0, 225, 255, 0.2);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--gradient-dark);
    color: var(--neutral-100);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--neutral-800);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}

/* ===== MODERN COMPONENTS ===== */

/* Glass Card Component */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.glass-card:hover::before {
    opacity: 1;
}

.glass-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl), var(--shadow-neon);
    border-color: rgba(91, 130, 255, 0.3);
}

/* Modern Button Styles */
.btn-modern {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    border-radius: var(--radius-lg);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    overflow: hidden;
    text-decoration: none;
    white-space: nowrap;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-primary-modern {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-neon);
}

.btn-secondary-modern {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-secondary-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.btn-ghost-modern {
    background: transparent;
    color: var(--neutral-300);
    border: 1px solid var(--neutral-600);
}

.btn-ghost-modern:hover {
    background: var(--glass-bg);
    color: var(--neutral-100);
    border-color: var(--primary-500);
    transform: translateY(-2px);
}

/* Modern Input Styles */
.input-modern {
    width: 100%;
    padding: 0.875rem 1rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--neutral-100);
    font-size: 0.875rem;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.input-modern::placeholder {
    color: var(--neutral-400);
}

.input-modern:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(91, 130, 255, 0.1);
    background: rgba(255, 255, 255, 0.15);
}

/* Modern Card Styles */
.card-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card-modern::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: none;
}

.card-modern:hover::after {
    opacity: 1;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(91, 130, 255, 0.3);
}

/* POS Container - Modern Layout */
.pos-container-modern {
    display: grid;
    grid-template-columns: 280px 1fr 380px;
    gap: 1.5rem;
    height: calc(100vh - 64px);
    padding: 1.5rem;
    max-width: 100vw;
    overflow: hidden;
}

@media (max-width: 1200px) {
    .pos-container-modern {
        grid-template-columns: 1fr 350px;
        grid-template-rows: auto 1fr;
    }
}

@media (max-width: 768px) {
    .pos-container-modern {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto 1fr;
        gap: 1rem;
        padding: 1rem;
    }
}

/* ===== MODERN POS SPECIFIC STYLES ===== */

/* Product Grid - Modern - Compact */
.products-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.75rem;
    padding: 1rem;
    overflow-y: auto;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
}

.product-card-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: 0.75rem;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.product-card-modern:hover::before {
    opacity: 0.1;
}

.product-card-modern:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl), var(--shadow-neon);
    border-color: var(--primary-500);
}

.product-card-modern.selected {
    border-color: var(--secondary-500);
    box-shadow: var(--shadow-glow);
}

.product-card-modern.selected::before {
    opacity: 0.15;
    background: var(--gradient-secondary);
}

.product-image-modern {
    width: 100%;
    height: 60px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    margin-bottom: 0.5rem;
    background: var(--neutral-700);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--neutral-400);
}

.product-name-modern {
    font-weight: 600;
    font-size: 0.75rem;
    color: var(--neutral-100);
    margin-bottom: 0.25rem;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product-price-modern {
    font-weight: 700;
    font-size: 0.875rem;
    color: var(--secondary-400);
    font-family: 'JetBrains Mono', monospace;
}

/* ===== NUMPAD COMPACT STYLES ===== */

.quantity-section-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1rem;
}

.quantity-display-modern {
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-md);
    padding: 0.75rem;
    border: 1px solid var(--glass-border);
}

.numpad-compact-modern {
    width: 100%;
}

.numpad-btn-compact {
    width: 100%;
    height: 40px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--neutral-100);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.numpad-btn-compact:hover {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.numpad-btn-compact:active {
    transform: translateY(0) scale(0.95);
}

.numpad-btn-compact.numpad-clear {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.numpad-btn-compact.numpad-clear:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
}

/* Cart - Modern */
.cart-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.cart-header-modern {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.cart-title-modern {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--neutral-100);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-items-modern {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1.5rem;
    padding-right: 0.5rem;
}

.cart-item-modern {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.cart-item-modern:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.cart-item-details-modern {
    flex: 1;
}

.cart-item-name-modern {
    font-weight: 600;
    color: var(--neutral-100);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.cart-item-quantity-modern {
    color: var(--neutral-400);
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-item-price-modern {
    font-weight: 700;
    color: var(--secondary-400);
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.875rem;
}

.cart-total-modern {
    padding-top: 1.5rem;
    border-top: 2px solid var(--glass-border);
    margin-top: auto;
}

.cart-total-amount-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--neutral-100);
}

.cart-total-amount-modern .amount {
    color: var(--accent-400);
    font-family: 'JetBrains Mono', monospace;
}

/* Quantity Controls */
.quantity-controls-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn-modern {
    width: 28px;
    height: 28px;
    border-radius: var(--radius-md);
    border: 1px solid var(--neutral-600);
    background: var(--glass-bg);
    color: var(--neutral-300);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.75rem;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

.quantity-btn-modern:hover {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.quantity-btn-modern:active {
    transform: scale(0.95);
}

/* Ensure quantity controls are always visible */
.cart-item-quantity-modern {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
}

.quantity-controls-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(0, 0, 0, 0.2);
    padding: 0.25rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--glass-border);
}

/* ===== MODERN NAVIGATION STYLES ===== */

/* Navigation Item */
.nav-item-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--neutral-300);
    background: transparent;
    border: 1px solid transparent;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.nav-item-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left var(--transition-slow);
}

.nav-item-modern:hover::before {
    left: 100%;
}

.nav-item-modern:hover {
    color: var(--neutral-100);
    background: var(--glass-bg);
    border-color: var(--glass-border);
    transform: translateY(-1px);
}

/* Navigation Dropdown - Completely Fixed */
.nav-dropdown-modern {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 280px;
    margin-top: 0.5rem;
    background: rgba(15, 23, 42, 0.98) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--radius-xl);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out, transform 0.3s ease-out;
    z-index: 9999 !important;
    pointer-events: none;
    padding: 1rem;
    display: block !important;
}

/* Show dropdown on hover with delay */
.group:hover .nav-dropdown-modern {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    pointer-events: auto;
    transition-delay: 0.15s;
}

/* Keep dropdown visible when hovering over it */
.nav-dropdown-modern:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

/* Extended hover zone - MUCH larger */
.group {
    position: relative;
}

.group::after {
    content: '';
    position: absolute;
    top: 100%;
    left: -30px;
    right: -30px;
    height: 1.5rem;
    z-index: 999;
    background: transparent;
}

/* Additional hover zone inside dropdown */
.nav-dropdown-modern::before {
    content: '';
    position: absolute;
    top: -1.5rem;
    left: -30px;
    right: -30px;
    height: 1.5rem;
    z-index: 998;
    background: transparent;
}

/* Navigation Dropdown Item - Super Enhanced */
.nav-dropdown-item-modern {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 1.25rem 1.5rem;
    margin: 0.25rem 0;
    border-radius: var(--radius-lg);
    color: var(--neutral-200);
    text-decoration: none;
    transition: all 0.2s ease-out;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    min-height: 60px;
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.4;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    gap: 0.75rem;
}

.nav-dropdown-item-modern:hover {
    color: white;
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
    transform: translateX(3px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
}

.nav-dropdown-item-modern:active {
    transform: translateX(1px) scale(0.98);
    background: rgba(59, 130, 246, 0.3);
}

.nav-dropdown-item-modern i {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    color: var(--primary-400);
}

.nav-dropdown-item-modern:hover i {
    color: white;
}

/* Ensure links are clickable - FIXED */
.nav-dropdown-item-modern {
    pointer-events: auto !important;
}

.nav-dropdown-item-modern * {
    pointer-events: auto !important;
}

/* ===== SIMPLE DROPDOWN SYSTEM ===== */

/* Navigation Button */
.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    color: white;
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(91, 130, 255, 0.4);
    transform: translateY(-1px);
}

/* Dropdown Container */
.dropdown-container {
    position: relative;
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 250px;
    margin-top: 0.5rem;
    background: rgba(15, 23, 42, 0.98);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.3s ease;
    z-index: 99999;
    padding: 0.75rem;
    pointer-events: none;
    backdrop-filter: blur(20px);
}

/* Show dropdown on hover */
.dropdown-container:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

/* Dropdown Item */
.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    margin: 0.25rem 0;
    color: rgba(255, 255, 255, 0.95);
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    min-height: 50px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    font-weight: 500;
}

.dropdown-item:hover {
    color: white;
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.6);
    transform: translateX(2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.dropdown-item i {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    color: rgba(91, 130, 255, 0.8);
}

.dropdown-item:hover i {
    color: white;
}

/* Extended hover zone */
.dropdown-container::after {
    content: '';
    position: absolute;
    top: 100%;
    left: -20px;
    right: -20px;
    height: 1rem;
    z-index: 9998;
}

/* Ensure dropdown items are always clickable */
.dropdown-item {
    pointer-events: auto !important;
    position: relative;
    z-index: 10000;
}

.dropdown-item span {
    pointer-events: none;
}

.dropdown-item i {
    pointer-events: none;
}



/* User Menu Modern */
.user-menu-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.user-menu-modern:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-500);
    transform: translateY(-1px);
}

.user-avatar-modern {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-lg);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 0.875rem;
}

/* Mobile Menu Button */
.mobile-menu-btn-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--neutral-300);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.mobile-menu-btn-modern:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--neutral-100);
    transform: scale(1.05);
}

/* Mobile Menu */
.mobile-menu-modern {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--neutral-900);
    z-index: 60;
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
}

.mobile-menu-modern.open {
    transform: translateX(0);
}

.mobile-menu-content-modern {
    padding: 2rem;
    height: 100%;
    overflow-y: auto;
}

.mobile-menu-item-modern {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: var(--radius-lg);
    color: var(--neutral-300);
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: all var(--transition-normal);
}

.mobile-menu-item-modern:hover {
    background: var(--glass-bg);
    color: var(--neutral-100);
    transform: translateX(8px);
}

/* Quick Actions */
.quick-actions-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-action-btn-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--neutral-300);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.quick-action-btn-modern:hover {
    background: var(--gradient-primary);
    border-color: var(--primary-500);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-neon);
}

.quick-action-btn-modern .badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 16px;
    height: 16px;
    background: var(--accent-500);
    border-radius: 50%;
    font-size: 0.625rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

/* ===== MODERN POS SPECIFIC COMPONENTS ===== */

/* Category Button Modern */
.category-btn-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--neutral-300);
    text-decoration: none;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.category-btn-modern:hover::before {
    opacity: 0.1;
}

.category-btn-modern:hover {
    color: var(--neutral-100);
    border-color: var(--primary-500);
    transform: translateX(4px);
}

.category-btn-modern.active {
    background: var(--gradient-primary);
    border-color: var(--primary-500);
    color: white;
    box-shadow: var(--shadow-neon);
}

.category-btn-modern.active::before {
    opacity: 0;
}

/* View Toggle Button */
.view-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    color: var(--neutral-400);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.view-toggle-btn:hover {
    color: var(--neutral-200);
    background: rgba(255, 255, 255, 0.1);
}

.view-toggle-btn.active {
    color: var(--primary-400);
    background: var(--primary-500)/20;
}

/* Numpad Modern */
.numpad-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
}

.numpad-display-modern {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1rem;
    text-align: center;
    font-family: 'JetBrains Mono', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: var(--neutral-100);
    margin-bottom: 1.5rem;
    min-height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.numpad-btn-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    border-radius: var(--radius-lg);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--neutral-100);
    font-size: 1.25rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.numpad-btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.numpad-btn-modern:hover::before {
    opacity: 0.2;
}

.numpad-btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-500);
}

.numpad-btn-modern:active {
    transform: translateY(0);
}

.numpad-btn-modern.clear {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border-color: #dc2626;
}

.numpad-btn-modern.clear:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.numpad-btn-modern.enter {
    background: var(--gradient-secondary);
    border-color: var(--secondary-500);
}

.numpad-btn-modern.enter:hover {
    box-shadow: var(--shadow-glow);
}

/* Payment Modal Modern */
.payment-modal-modern {
    position: fixed;
    inset: 0;
    z-index: 60;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.payment-modal-modern.open {
    opacity: 1;
    visibility: visible;
}

.payment-modal-content-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: transform var(--transition-normal);
}

.payment-modal-modern.open .payment-modal-content-modern {
    transform: scale(1) translateY(0);
}

/* ===== TABLE MANAGEMENT STYLES ===== */

/* Table Card Modern */
.table-card-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.table-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: none;
}

.table-card-modern:hover::before {
    opacity: 1;
}

.table-card-modern:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

/* Table Status Variants */
.table-free {
    border-color: var(--accent-500);
}

.table-free:hover {
    box-shadow: var(--shadow-xl), 0 0 30px rgba(34, 197, 94, 0.3);
}

.table-occupied {
    border-color: var(--primary-500);
}

.table-occupied:hover {
    box-shadow: var(--shadow-xl), var(--shadow-neon);
}

.table-reserved {
    border-color: #eab308;
}

.table-reserved:hover {
    box-shadow: var(--shadow-xl), 0 0 30px rgba(234, 179, 8, 0.3);
}

/* Table Card Header */
.table-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.table-number {
    font-size: 1.5rem;
    font-weight: 800;
    font-family: 'JetBrains Mono', monospace;
    color: var(--neutral-100);
}

.table-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.table-free .table-status-indicator {
    background: var(--accent-500);
}

.table-occupied .table-status-indicator {
    background: var(--primary-500);
}

.table-reserved .table-status-indicator {
    background: #eab308;
}

/* Table Card Body */
.table-card-body {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-capacity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--neutral-300);
    font-size: 0.875rem;
}

.table-status-text {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table-free .table-status-text {
    color: var(--accent-400);
}

.table-occupied .table-status-text {
    color: var(--primary-400);
}

.table-reserved .table-status-text {
    color: #eab308;
}

/* Table Actions */
.table-actions-modern {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.table-card-modern:hover .table-actions-modern {
    opacity: 1;
}

.table-action-btn-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--neutral-300);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.table-action-btn-modern:hover {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: scale(1.1);
}

/* Restaurant Floor Plan */
.floor-plan-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.floor-plan-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(91, 130, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 225, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* Zone Indicators */
.zone-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 1rem;
    z-index: 10;
}

.zone-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    color: var(--neutral-300);
}

.zone-badge .zone-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.zone-main .zone-dot {
    background: var(--primary-500);
}

.zone-terrace .zone-dot {
    background: var(--secondary-500);
}

/* ===== KITCHEN INTERFACE STYLES ===== */

/* Kitchen Order Card */
.kitchen-order-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.kitchen-order-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: none;
}

.kitchen-order-card:hover::before {
    opacity: 1;
}

.kitchen-order-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-500);
}

/* Kitchen Order Status Variants */
.kitchen-order-card.urgent {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.kitchen-order-card.urgent:hover {
    box-shadow: var(--shadow-lg), 0 0 20px rgba(239, 68, 68, 0.3);
}

.kitchen-order-card.priority {
    border-color: #eab308;
    background: rgba(234, 179, 8, 0.1);
}

.kitchen-order-card.priority:hover {
    box-shadow: var(--shadow-lg), 0 0 20px rgba(234, 179, 8, 0.3);
}

/* Kitchen Timer */
.kitchen-timer {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-sm);
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.75rem;
    font-weight: 600;
}

.kitchen-timer.urgent {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    animation: pulse 1s infinite;
}

.kitchen-timer.warning {
    background: rgba(234, 179, 8, 0.2);
    color: #eab308;
}

.kitchen-timer.normal {
    color: var(--neutral-300);
}

/* Kitchen Status Indicators */
.kitchen-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.kitchen-status-pending {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.kitchen-status-cooking {
    background: rgba(234, 179, 8, 0.2);
    color: #eab308;
    border: 1px solid rgba(234, 179, 8, 0.3);
}

.kitchen-status-ready {
    background: rgba(34, 197, 94, 0.2);
    color: var(--accent-400);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

/* Kitchen Action Buttons */
.btn-accent-modern {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-md);
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-accent-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-accent-modern:hover::before {
    left: 100%;
}

.btn-accent-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

/* Kitchen Board Layout */
.kitchen-board {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    min-height: 400px;
}

.kitchen-column {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.kitchen-column::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.kitchen-column.pending::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.kitchen-column.cooking::before {
    background: linear-gradient(90deg, #eab308, #d97706);
}

.kitchen-column.ready::before {
    background: linear-gradient(90deg, var(--accent-500), var(--accent-600));
}

/* Kitchen Order Items */
.kitchen-order-items {
    list-style: none;
    padding: 0;
    margin: 0;
    space-y: 0.5rem;
}

.kitchen-order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.875rem;
}

.kitchen-order-item:last-child {
    border-bottom: none;
}

.kitchen-order-quantity {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: var(--primary-500);
    color: white;
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 0.5rem;
}

.kitchen-order-notes {
    font-size: 0.75rem;
    color: #eab308;
    font-style: italic;
    margin-top: 0.25rem;
}

/* Kitchen Notifications */
.kitchen-notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1rem;
    color: var(--neutral-100);
    box-shadow: var(--shadow-xl);
    animation: slideInFromRight 0.3s ease-out;
}

.kitchen-notification.urgent {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.kitchen-notification.success {
    border-color: var(--accent-500);
    background: rgba(34, 197, 94, 0.1);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Styles pour la page de détail du produit */
.product-detail {
    animation: fadeIn 0.3s ease-out;
}

.product-detail .card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.product-detail .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.product-detail .stat-card {
    transition: transform 0.2s ease-in-out;
}

.product-detail .stat-card:hover {
    transform: scale(1.02);
}

.product-detail .alert-card {
    transition: transform 0.2s ease-in-out;
}

.product-detail .alert-card:hover {
    transform: scale(1.02);
}

/* Styles pour les tableaux */
.table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-container table {
    min-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table-container th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #f9fafb;
}

.dark .table-container th {
    background-color: #1f2937;
}

/* Styles pour les badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    transition: all 0.2s ease-in-out;
}

.badge:hover {
    transform: scale(1.05);
}

/* Styles pour les boutons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

/* Styles pour les cartes */
.card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease-in-out;
}

.dark .card {
    background-color: #1f2937;
}

.card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Styles pour les formulaires */
.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.dark .form-input {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Styles pour les icônes */
.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    transition: all 0.2s ease-in-out;
}

.icon:hover {
    transform: scale(1.1);
}

/* Styles pour les transitions */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Styles pour les effets de survol */
.hover-scale {
    transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Styles pour les effets de focus */
.focus-ring {
    outline: none;
    transition: box-shadow 0.2s ease-in-out;
}

.focus-ring:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Styles pour les effets de pression */
.active-scale {
    transition: transform 0.1s ease-in-out;
}

.active-scale:active {
    transform: scale(0.95);
}