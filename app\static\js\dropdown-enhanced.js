/**
 * Enhanced Dropdown System for FuturePOS
 * Fixes dropdown link visibility and clickability issues
 */

class EnhancedDropdown {
    constructor(container) {
        this.container = container;
        this.menu = container.querySelector('.dropdown-menu');
        this.button = container.querySelector('.nav-btn');
        this.items = container.querySelectorAll('.dropdown-item');
        
        this.isVisible = false;
        this.showTimeout = null;
        this.hideTimeout = null;
        this.isHoveringContainer = false;
        this.isHoveringMenu = false;
        
        this.init();
    }
    
    init() {
        if (!this.menu || !this.button) return;
        
        this.setupEventListeners();
        this.setupKeyboardNavigation();
        this.setupAccessibility();
        this.createHoverZone();
    }
    
    createHoverZone() {
        // Create an invisible hover zone between button and menu
        const hoverZone = document.createElement('div');
        hoverZone.className = 'dropdown-hover-zone';
        hoverZone.style.cssText = `
            position: absolute;
            top: 100%;
            left: -30px;
            right: -30px;
            height: 20px;
            z-index: 9998;
            background: transparent;
            pointer-events: auto;
        `;
        
        this.container.appendChild(hoverZone);
        this.hoverZone = hoverZone;
        
        // Add hover events to the zone
        hoverZone.addEventListener('mouseenter', () => {
            this.isHoveringContainer = true;
            this.show();
        });
        
        hoverZone.addEventListener('mouseleave', () => {
            this.isHoveringContainer = false;
            this.scheduleHide();
        });
    }
    
    setupEventListeners() {
        // Container events
        this.container.addEventListener('mouseenter', () => {
            this.isHoveringContainer = true;
            this.show();
        });
        
        this.container.addEventListener('mouseleave', (e) => {
            this.isHoveringContainer = false;
            
            // Smart hiding: check if mouse is moving towards menu
            const rect = this.menu.getBoundingClientRect();
            const buffer = 50;
            
            if (this.isMouseNearMenu(e.clientX, e.clientY, rect, buffer)) {
                // Delay hiding if mouse is near menu
                setTimeout(() => {
                    if (!this.isHoveringMenu) this.scheduleHide();
                }, 200);
            } else {
                this.scheduleHide();
            }
        });
        
        // Menu events
        this.menu.addEventListener('mouseenter', () => {
            this.isHoveringMenu = true;
            this.cancelHide();
        });
        
        this.menu.addEventListener('mouseleave', () => {
            this.isHoveringMenu = false;
            this.scheduleHide();
        });
        
        // Item events
        this.items.forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleItemClick(item, e);
            });
            
            item.addEventListener('mouseenter', () => {
                this.focusItem(item);
            });
        });
        
        // Global click to close
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.hide();
            }
        });
    }
    
    setupKeyboardNavigation() {
        this.button.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowDown':
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    this.show();
                    this.focusFirstItem();
                    break;
                case 'Escape':
                    this.hide();
                    break;
            }
        });
        
        this.items.forEach((item, index) => {
            item.addEventListener('keydown', (e) => {
                switch(e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        this.focusNextItem(index);
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        this.focusPrevItem(index);
                        break;
                    case 'Enter':
                    case ' ':
                        e.preventDefault();
                        this.handleItemClick(item, e);
                        break;
                    case 'Escape':
                        e.preventDefault();
                        this.hide();
                        this.button.focus();
                        break;
                }
            });
        });
    }
    
    setupAccessibility() {
        // Add ARIA attributes
        this.button.setAttribute('aria-haspopup', 'true');
        this.button.setAttribute('aria-expanded', 'false');
        this.menu.setAttribute('role', 'menu');
        
        this.items.forEach(item => {
            item.setAttribute('role', 'menuitem');
            item.setAttribute('tabindex', '-1');
        });
    }
    
    isMouseNearMenu(mouseX, mouseY, rect, buffer) {
        return mouseX >= rect.left - buffer && 
               mouseX <= rect.right + buffer && 
               mouseY >= rect.top - buffer && 
               mouseY <= rect.bottom + buffer;
    }
    
    show() {
        this.cancelHide();
        
        if (this.isVisible) return;
        
        this.showTimeout = setTimeout(() => {
            this.menu.style.opacity = '1';
            this.menu.style.visibility = 'visible';
            this.menu.style.transform = 'translateY(0) scale(1)';
            this.menu.style.pointerEvents = 'auto';
            this.menu.style.zIndex = '99999';
            
            this.isVisible = true;
            this.button.setAttribute('aria-expanded', 'true');
        }, 50);
    }
    
    hide() {
        this.cancelShow();
        
        if (!this.isVisible) return;
        
        this.menu.style.opacity = '0';
        this.menu.style.visibility = 'hidden';
        this.menu.style.transform = 'translateY(-15px) scale(0.95)';
        this.menu.style.pointerEvents = 'none';
        
        this.isVisible = false;
        this.button.setAttribute('aria-expanded', 'false');
    }
    
    scheduleHide() {
        this.hideTimeout = setTimeout(() => {
            if (!this.isHoveringContainer && !this.isHoveringMenu) {
                this.hide();
            }
        }, 150);
    }
    
    cancelShow() {
        if (this.showTimeout) {
            clearTimeout(this.showTimeout);
            this.showTimeout = null;
        }
    }
    
    cancelHide() {
        if (this.hideTimeout) {
            clearTimeout(this.hideTimeout);
            this.hideTimeout = null;
        }
    }
    
    handleItemClick(item, event) {
        // Ensure the click works
        if (item.href) {
            window.location.href = item.href;
        } else if (item.onclick) {
            item.onclick(event);
        }
        this.hide();
    }
    
    focusItem(item) {
        this.items.forEach(i => i.setAttribute('tabindex', '-1'));
        item.setAttribute('tabindex', '0');
        item.focus();
    }
    
    focusFirstItem() {
        if (this.items.length > 0) {
            this.focusItem(this.items[0]);
        }
    }
    
    focusNextItem(currentIndex) {
        const nextIndex = (currentIndex + 1) % this.items.length;
        this.focusItem(this.items[nextIndex]);
    }
    
    focusPrevItem(currentIndex) {
        const prevIndex = currentIndex === 0 ? this.items.length - 1 : currentIndex - 1;
        this.focusItem(this.items[prevIndex]);
    }
}

// Initialize enhanced dropdowns when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const dropdownContainers = document.querySelectorAll('.dropdown-container');
    dropdownContainers.forEach(container => {
        new EnhancedDropdown(container);
    });
});

// Export for use in other scripts
window.EnhancedDropdown = EnhancedDropdown;
